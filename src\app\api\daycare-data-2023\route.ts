import fs from "fs";
import path from "path";
import { NextRequest, NextResponse } from "next/server";
import type { DaycareData, DaycareGeoJSON, ApiResponse } from "@/types/daycare";

/**
 * GET /api/daycare-data-2023
 * 获取2023年托儿所数据
 *
 * Query Parameters:
 * - zip: 特定ZIP码 (可选)
 * - format: 'json' | 'geojson' (默认: 'json')
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const zip = searchParams.get("zip");
    const format = searchParams.get("format") || "json";

    // 根据格式选择数据文件
    let dataPath: string;
    let data: DaycareData[] | DaycareGeoJSON;

    if (format === "geojson") {
      dataPath = path.join(
        process.cwd(),
        "data/processed_2023/daycare_data_2023.geojson"
      );

      try {
        const geoJsonContent = fs.readFileSync(dataPath, "utf8");
        console.error("GeoJSON file size:", geoJsonContent.length);
        console.error("First 100 chars:", geoJsonContent.substring(0, 100));
        data = JSON.parse(geoJsonContent) as DaycareGeoJSON;
      } catch (parseError) {
        console.error("Error parsing GeoJSON file:", parseError);
        return NextResponse.json(
          {
            success: false,
            error: "Failed to parse daycare GeoJSON data",
            details:
              parseError instanceof Error
                ? parseError.message
                : "Unknown parsing error",
            timestamp: new Date().toISOString(),
          } as ApiResponse<null>,
          { status: 500 }
        );
      }

      // 如果请求特定ZIP码，过滤GeoJSON features
      if (zip) {
        const filteredFeatures = data.features.filter(
          (feature) => feature.properties.zip_code === zip
        );

        if (filteredFeatures.length === 0) {
          return NextResponse.json(
            {
              success: false,
              error: `ZIP code ${zip} not found in 2023 data`,
              timestamp: new Date().toISOString(),
            } as ApiResponse<null>,
            { status: 404 }
          );
        }

        data = {
          ...data,
          features: filteredFeatures,
          metadata: {
            ...data.metadata,
            total_features: filteredFeatures.length,
          },
        };
      }
    } else {
      // 返回JSON格式
      dataPath = path.join(
        process.cwd(),
        "data/processed_2023/api_data_2023.json"
      );

      try {
        const jsonContent = fs.readFileSync(dataPath, "utf8");
        console.error("JSON file size:", jsonContent.length);
        console.error("First 100 chars:", jsonContent.substring(0, 100));
        data = JSON.parse(jsonContent) as DaycareData[];
      } catch (parseError) {
        console.error("Error parsing JSON file:", parseError);
        return NextResponse.json(
          {
            success: false,
            error: "Failed to parse daycare JSON data",
            details:
              parseError instanceof Error
                ? parseError.message
                : "Unknown parsing error",
            timestamp: new Date().toISOString(),
          },
          { status: 500 }
        );
      }

      // 如果请求特定ZIP码
      if (zip) {
        const zipData = data.find((item: DaycareData) => item.zip_code === zip);

        if (!zipData) {
          return NextResponse.json(
            {
              success: false,
              error: `ZIP code ${zip} not found in 2023 data`,
              timestamp: new Date().toISOString(),
            } as ApiResponse<null>,
            { status: 404 }
          );
        }

        return NextResponse.json({
          success: true,
          data: zipData,
          timestamp: new Date().toISOString(),
        } as ApiResponse<DaycareData>);
      }
    }

    // 返回成功响应
    return NextResponse.json({
      success: true,
      data,
      timestamp: new Date().toISOString(),
    } as ApiResponse<DaycareData[] | DaycareGeoJSON>);
  } catch (error) {
    console.error("Error loading 2023 daycare data:", error);

    return NextResponse.json(
      {
        success: false,
        error: "Failed to load 2023 daycare data",
        timestamp: new Date().toISOString(),
      } as ApiResponse<null>,
      { status: 500 }
    );
  }
}

/**
 * 验证数据文件是否存在
 */
function validateDataFiles(): { isValid: boolean; missingFiles: string[] } {
  const requiredFiles = [
    "data/processed_2023/daycare_data_2023.geojson",
    "data/processed_2023/api_data_2023.json",
    "data/processed_2023/stats_2023.json",
  ];

  const missingFiles: string[] = [];

  for (const file of requiredFiles) {
    const filePath = path.join(process.cwd(), file);
    if (!fs.existsSync(filePath)) {
      missingFiles.push(file);
    }
  }

  return {
    isValid: missingFiles.length === 0,
    missingFiles,
  };
}

/**
 * HEAD /api/daycare-data-2023
 * 检查数据文件状态
 */
export async function HEAD() {
  try {
    const validation = validateDataFiles();

    if (!validation.isValid) {
      return new NextResponse(null, {
        status: 503,
        headers: {
          "X-Missing-Files": validation.missingFiles.join(","),
        },
      });
    }

    return new NextResponse(null, {
      status: 200,
      headers: {
        "X-Data-Status": "available",
        "X-Last-Modified": new Date().toISOString(),
      },
    });
  } catch (error) {
    console.error("Error checking data files:", error);
    return new NextResponse(null, { status: 500 });
  }
}
